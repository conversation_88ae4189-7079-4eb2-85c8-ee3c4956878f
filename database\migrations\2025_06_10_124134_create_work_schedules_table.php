<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('daily_hours', 4, 2)->default(8.00); // Horas por dia
            $table->json('work_days')->default('["monday","tuesday","wednesday","thursday","friday"]'); // Dias da semana
            $table->time('default_start_time')->default('09:00:00'); // Horário padrão de entrada
            $table->time('lunch_break_duration')->default('01:00:00'); // Duração do almoço
            $table->boolean('flexible_schedule')->default(false); // Horário flexível
            $table->decimal('overtime_threshold', 4, 2)->default(8.00); // Limite para hora extra
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_schedules');
    }
};
