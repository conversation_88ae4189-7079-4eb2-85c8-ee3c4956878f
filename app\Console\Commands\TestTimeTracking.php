<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\WorkSchedule;
use App\Models\TimeEntry;
use Illuminate\Console\Command;

class TestTimeTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:time-tracking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testa o sistema de controle de horas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testando o sistema de controle de horas...');

        // Busca o usuário de teste
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            $this->error('Usuário de teste não encontrado!');
            return;
        }

        $this->info("Usuário encontrado: {$user->name}");

        // Verifica se tem configuração de horário
        $workSchedule = $user->workSchedule;
        if (!$workSchedule) {
            $this->info('Criando configuração de horário padrão...');
            $workSchedule = WorkSchedule::create([
                'user_id' => $user->id,
                'daily_hours' => 8.00,
                'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'default_start_time' => '09:00:00',
                'lunch_break_duration' => '01:00:00',
                'flexible_schedule' => false,
                'overtime_threshold' => 8.00,
            ]);
        }

        $this->info('Configuração de horário: OK');

        // Verifica registros de hoje
        $todayEntry = $user->timeEntries()->whereDate('date', today())->first();

        if ($todayEntry) {
            $this->info('Registro de hoje encontrado:');
            $this->line("- Entrada: " . ($todayEntry->clock_in ?? 'Não registrada'));
            $this->line("- Saída: " . ($todayEntry->clock_out ?? 'Não registrada'));
            $this->line("- Status: " . $todayEntry->status);
        } else {
            $this->info('Nenhum registro de hoje encontrado.');
        }

        $this->info('Sistema de controle de horas funcionando corretamente!');
        $this->info('Acesse: http://127.0.0.1:8000/time-tracking');
    }
}
