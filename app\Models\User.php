<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Relacionamento com a configuração de horário de trabalho
     */
    public function workSchedule()
    {
        return $this->hasOne(\App\Models\WorkSchedule::class);
    }

    /**
     * Relacionamento com os registros de ponto
     */
    public function timeEntries()
    {
        return $this->hasMany(\App\Models\TimeEntry::class);
    }

    /**
     * Obtém o registro de ponto de hoje
     */
    public function todayTimeEntry()
    {
        return $this->timeEntries()->whereDate('date', today());
    }

    /**
     * Verifica se o usuário já bateu o ponto hoje
     */
    public function hasClockedInToday(): bool
    {
        return $this->todayTimeEntry()->whereNotNull('clock_in')->exists();
    }

    /**
     * Verifica se o usuário já saiu hoje
     */
    public function hasClockedOutToday(): bool
    {
        return $this->todayTimeEntry()->whereNotNull('clock_out')->exists();
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }
}
