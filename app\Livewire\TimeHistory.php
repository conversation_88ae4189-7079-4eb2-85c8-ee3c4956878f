<?php

namespace App\Livewire;

use App\Models\TimeEntry;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class TimeHistory extends Component
{
    use WithPagination;

    public $startDate;
    public $endDate;
    public $filterStatus = 'all';

    public function mount()
    {
        // Define período padrão: último mês
        $this->endDate = now()->format('Y-m-d');
        $this->startDate = now()->subMonth()->format('Y-m-d');
    }

    public function updatedStartDate()
    {
        $this->resetPage();
    }

    public function updatedEndDate()
    {
        $this->resetPage();
    }

    public function updatedFilterStatus()
    {
        $this->resetPage();
    }

    public function getTimeEntriesProperty()
    {
        $query = Auth::user()->timeEntries()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->orderBy('date', 'desc');

        if ($this->filterStatus !== 'all') {
            $query->where('status', $this->filterStatus);
        }

        return $query->paginate(15);
    }

    public function getTotalHoursProperty()
    {
        return Auth::user()->timeEntries()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->sum('total_hours');
    }

    public function getTotalOvertimeProperty()
    {
        return Auth::user()->timeEntries()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->sum('overtime_hours');
    }

    public function getAverageHoursProperty()
    {
        $entries = Auth::user()->timeEntries()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->whereNotNull('total_hours');

        $count = $entries->count();
        $total = $entries->sum('total_hours');

        return $count > 0 ? round($total / $count, 2) : 0;
    }

    public function exportToCsv()
    {
        $entries = Auth::user()->timeEntries()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->orderBy('date', 'desc')
            ->get();

        $filename = 'relatorio_horas_' . $this->startDate . '_' . $this->endDate . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($entries) {
            $file = fopen('php://output', 'w');

            // Cabeçalho do CSV
            fputcsv($file, [
                'Data',
                'Entrada',
                'Saída',
                'Início Almoço',
                'Fim Almoço',
                'Total Horas',
                'Horas Extras',
                'Status',
                'Observações'
            ]);

            // Dados
            foreach ($entries as $entry) {
                fputcsv($file, [
                    $entry->date->format('d/m/Y'),
                    $entry->clock_in ? Carbon::createFromFormat('H:i:s', $entry->clock_in)->format('H:i') : '',
                    $entry->clock_out ? Carbon::createFromFormat('H:i:s', $entry->clock_out)->format('H:i') : '',
                    $entry->lunch_start ? Carbon::createFromFormat('H:i:s', $entry->lunch_start)->format('H:i') : '',
                    $entry->lunch_end ? Carbon::createFromFormat('H:i:s', $entry->lunch_end)->format('H:i') : '',
                    $entry->total_hours ?? '',
                    $entry->overtime_hours ?? '',
                    $entry->status === 'completed' ? 'Concluído' : 'Em andamento',
                    $entry->notes ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function render()
    {
        return view('livewire.time-history', [
            'timeEntries' => $this->timeEntries,
            'totalHours' => $this->totalHours,
            'totalOvertime' => $this->totalOvertime,
            'averageHours' => $this->averageHours,
        ]);
    }
}
