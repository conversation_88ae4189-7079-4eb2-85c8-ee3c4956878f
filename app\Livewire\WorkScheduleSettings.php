<?php

namespace App\Livewire;

use App\Models\WorkSchedule;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class WorkScheduleSettings extends Component
{
    public $dailyHours = 8.00;
    public $workDays = [];
    public $defaultStartTime = '09:00';
    public $lunchBreakDuration = '01:00';
    public $flexibleSchedule = false;
    public $overtimeThreshold = 8.00;

    public $availableDays = [
        'monday' => 'Segunda-feira',
        'tuesday' => 'Terça-feira',
        'wednesday' => 'Quarta-feira',
        'thursday' => 'Quinta-feira',
        'friday' => 'Sexta-feira',
        'saturday' => 'Sábado',
        'sunday' => 'Domingo',
    ];

    public function mount()
    {
        $this->loadWorkSchedule();
    }

    public function loadWorkSchedule()
    {
        $user = Auth::user();
        $workSchedule = $user->workSchedule;

        if ($workSchedule) {
            $this->dailyHours = $workSchedule->daily_hours;
            $this->workDays = $workSchedule->work_days ?? [];
            $this->defaultStartTime = \Carbon\Carbon::createFromFormat('H:i:s', $workSchedule->default_start_time)->format('H:i');
            $this->lunchBreakDuration = \Carbon\Carbon::createFromFormat('H:i:s', $workSchedule->lunch_break_duration)->format('H:i');
            $this->flexibleSchedule = $workSchedule->flexible_schedule;
            $this->overtimeThreshold = $workSchedule->overtime_threshold;
        } else {
            // Valores padrão
            $this->workDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        }
    }

    public function rules()
    {
        return [
            'dailyHours' => 'required|numeric|min:1|max:24',
            'workDays' => 'required|array|min:1',
            'workDays.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'defaultStartTime' => 'required|date_format:H:i',
            'lunchBreakDuration' => 'required|date_format:H:i',
            'flexibleSchedule' => 'boolean',
            'overtimeThreshold' => 'required|numeric|min:1|max:24',
        ];
    }

    public function save()
    {
        $this->validate();

        $user = Auth::user();

        $workScheduleData = [
            'user_id' => $user->id,
            'daily_hours' => $this->dailyHours,
            'work_days' => $this->workDays,
            'default_start_time' => $this->defaultStartTime . ':00',
            'lunch_break_duration' => $this->lunchBreakDuration . ':00',
            'flexible_schedule' => $this->flexibleSchedule,
            'overtime_threshold' => $this->overtimeThreshold,
        ];

        WorkSchedule::updateOrCreate(
            ['user_id' => $user->id],
            $workScheduleData
        );

        session()->flash('success', 'Configurações de horário salvas com sucesso!');
    }

    public function resetToDefaults()
    {
        $this->dailyHours = 8.00;
        $this->workDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $this->defaultStartTime = '09:00';
        $this->lunchBreakDuration = '01:00';
        $this->flexibleSchedule = false;
        $this->overtimeThreshold = 8.00;
    }

    public function render()
    {
        return view('livewire.work-schedule-settings');
    }
}
