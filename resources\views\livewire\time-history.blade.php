<div class="max-w-6xl mx-auto p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Histórico de Horas</h1>

        <flux:button wire:click="exportToCsv" variant="outline" class="mt-4 md:mt-0">
            <flux:icon name="arrow-down-tray" class="w-5 h-5 mr-2" />
            Exportar CSV
        </flux:button>
    </div>

    <!-- Filtros -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filtros</h3>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <flux:input
                    wire:model.live="startDate"
                    label="Data Inicial"
                    type="date"
                />
            </div>

            <div>
                <flux:input
                    wire:model.live="endDate"
                    label="Data Final"
                    type="date"
                />
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                </label>
                <select
                    wire:model.live="filterStatus"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                    <option value="all">Todos</option>
                    <option value="completed">Concluído</option>
                    <option value="in_progress">Em andamento</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Total de Horas</h3>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                {{ number_format($totalHours, 2) }}h
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Horas Extras</h3>
            <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">
                {{ number_format($totalOvertime, 2) }}h
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Média Diária</h3>
            <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                {{ number_format($averageHours, 2) }}h
            </div>
        </div>
    </div>

    <!-- Tabela de Registros -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Registros</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Data
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Entrada
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Saída
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Almoço
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Total
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Extras
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Status
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($timeEntries as $entry)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $entry->date->format('d/m/Y') }}
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $entry->date->format('l') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-white">
                                {{ $entry->clock_in ? \Carbon\Carbon::createFromFormat('H:i:s', $entry->clock_in)->format('H:i') : '--:--' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-white">
                                {{ $entry->clock_out ? \Carbon\Carbon::createFromFormat('H:i:s', $entry->clock_out)->format('H:i') : '--:--' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-white">
                                @if($entry->lunch_start && $entry->lunch_end)
                                    {{ \Carbon\Carbon::createFromFormat('H:i:s', $entry->lunch_start)->format('H:i') }} -
                                    {{ \Carbon\Carbon::createFromFormat('H:i:s', $entry->lunch_end)->format('H:i') }}
                                @else
                                    --:-- - --:--
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-white">
                                {{ $entry->total_hours ? number_format($entry->total_hours, 2) . 'h' : '--' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono">
                                @if($entry->overtime_hours > 0)
                                    <span class="text-orange-600 dark:text-orange-400">
                                        {{ number_format($entry->overtime_hours, 2) }}h
                                    </span>
                                @else
                                    <span class="text-gray-500 dark:text-gray-400">--</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($entry->status === 'completed')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                        Concluído
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                        Em andamento
                                    </span>
                                @endif
                            </td>
                        </tr>
                        @if($entry->notes)
                            <tr class="bg-gray-50 dark:bg-gray-700">
                                <td colspan="7" class="px-6 py-2 text-sm text-gray-600 dark:text-gray-400">
                                    <strong>Observações:</strong> {{ $entry->notes }}
                                </td>
                            </tr>
                        @endif
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                                <flux:icon name="clock" class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
                                <p>Nenhum registro encontrado no período selecionado.</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        @if($timeEntries->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $timeEntries->links() }}
            </div>
        @endif
    </div>
</div>
