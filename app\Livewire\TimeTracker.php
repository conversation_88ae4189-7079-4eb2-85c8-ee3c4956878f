<?php

namespace App\Livewire;

use App\Models\TimeEntry;
use App\Models\WorkSchedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class TimeTracker extends Component
{
    public $currentTime;
    public $todayEntry;
    public $workSchedule;
    public $estimatedClockOut;
    public $notes = '';
    public $isOnLunch = false;

    public function mount()
    {
        $this->loadData();
        $this->updateCurrentTime();
    }

    public function loadData()
    {
        $user = Auth::user();

        // Carrega ou cria configuração de horário
        $this->workSchedule = $user->workSchedule ?? $this->createDefaultWorkSchedule($user);

        // Carrega registro de hoje
        $this->todayEntry = $user->timeEntries()
            ->whereDate('date', today())
            ->first();

        $this->calculateEstimatedClockOut();
    }

    public function createDefaultWorkSchedule($user)
    {
        return WorkSchedule::create([
            'user_id' => $user->id,
            'daily_hours' => 8.00,
            'work_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            'default_start_time' => '09:00:00',
            'lunch_break_duration' => '01:00:00',
            'flexible_schedule' => false,
            'overtime_threshold' => 8.00,
        ]);
    }

    public function updateCurrentTime()
    {
        $this->currentTime = now()->format('H:i:s');
    }

    public function clockIn()
    {
        $user = Auth::user();

        if ($this->todayEntry) {
            session()->flash('error', 'Você já registrou entrada hoje.');
            return;
        }

        $this->todayEntry = TimeEntry::create([
            'user_id' => $user->id,
            'date' => today(),
            'clock_in' => now()->format('H:i:s'),
            'status' => 'in_progress',
        ]);

        $this->calculateEstimatedClockOut();
        session()->flash('success', 'Entrada registrada com sucesso!');
    }

    public function clockOut()
    {
        if (!$this->todayEntry || !$this->todayEntry->clock_in) {
            session()->flash('error', 'Você precisa registrar a entrada primeiro.');
            return;
        }

        if ($this->todayEntry->clock_out) {
            session()->flash('error', 'Você já registrou a saída hoje.');
            return;
        }

        $this->todayEntry->update([
            'clock_out' => now()->format('H:i:s'),
            'notes' => $this->notes,
        ]);

        $this->todayEntry->markAsCompleted();
        $this->todayEntry->refresh();

        session()->flash('success', 'Saída registrada com sucesso!');
    }

    public function startLunch()
    {
        if (!$this->todayEntry || !$this->todayEntry->clock_in) {
            session()->flash('error', 'Você precisa registrar a entrada primeiro.');
            return;
        }

        if ($this->todayEntry->lunch_start) {
            session()->flash('error', 'Você já iniciou o almoço hoje.');
            return;
        }

        $this->todayEntry->update([
            'lunch_start' => now()->format('H:i:s'),
        ]);

        $this->isOnLunch = true;
        session()->flash('success', 'Início do almoço registrado!');
    }

    public function endLunch()
    {
        if (!$this->todayEntry || !$this->todayEntry->lunch_start) {
            session()->flash('error', 'Você precisa iniciar o almoço primeiro.');
            return;
        }

        if ($this->todayEntry->lunch_end) {
            session()->flash('error', 'Você já finalizou o almoço hoje.');
            return;
        }

        $this->todayEntry->update([
            'lunch_end' => now()->format('H:i:s'),
        ]);

        $this->isOnLunch = false;
        $this->calculateEstimatedClockOut();
        session()->flash('success', 'Fim do almoço registrado!');
    }

    public function calculateEstimatedClockOut()
    {
        if (!$this->todayEntry || !$this->todayEntry->clock_in || !$this->workSchedule) {
            $this->estimatedClockOut = null;
            return;
        }

        try {
            $clockIn = Carbon::createFromFormat('H:i:s', $this->todayEntry->clock_in);
            $dailyHours = $this->workSchedule->daily_hours;
            $lunchDuration = Carbon::createFromFormat('H:i:s', $this->workSchedule->lunch_break_duration);

            $estimatedOut = $clockIn
                ->addHours($dailyHours)
                ->addHours($lunchDuration->hour)
                ->addMinutes($lunchDuration->minute);

            $this->estimatedClockOut = $estimatedOut->format('H:i');
        } catch (\Exception $e) {
            $this->estimatedClockOut = null;
        }
    }

    public function getCurrentWorkedHours()
    {
        if (!$this->todayEntry || !$this->todayEntry->clock_in) {
            return '00:00';
        }

        $clockIn = Carbon::createFromFormat('H:i:s', $this->todayEntry->clock_in);
        $now = now();

        $workedMinutes = $now->diffInMinutes($clockIn);

        // Subtrai tempo de almoço se aplicável
        if ($this->todayEntry->lunch_start && $this->todayEntry->lunch_end) {
            $lunchStart = Carbon::createFromFormat('H:i:s', $this->todayEntry->lunch_start);
            $lunchEnd = Carbon::createFromFormat('H:i:s', $this->todayEntry->lunch_end);
            $lunchMinutes = $lunchEnd->diffInMinutes($lunchStart);
            $workedMinutes -= $lunchMinutes;
        } elseif ($this->todayEntry->lunch_start && !$this->todayEntry->lunch_end) {
            // Se está no almoço, subtrai o tempo desde o início
            $lunchStart = Carbon::createFromFormat('H:i:s', $this->todayEntry->lunch_start);
            $lunchMinutes = $now->diffInMinutes($lunchStart);
            $workedMinutes -= $lunchMinutes;
        }

        $hours = intval($workedMinutes / 60);
        $minutes = $workedMinutes % 60;

        return sprintf('%02d:%02d', $hours, $minutes);
    }

    public function getStatus()
    {
        if (!$this->todayEntry) {
            return 'not_started';
        }

        if ($this->todayEntry->clock_out) {
            return 'completed';
        }

        if ($this->todayEntry->lunch_start && !$this->todayEntry->lunch_end) {
            return 'on_lunch';
        }

        if ($this->todayEntry->clock_in) {
            return 'working';
        }

        return 'not_started';
    }

    public function render()
    {
        $this->updateCurrentTime();

        return view('livewire.time-tracker', [
            'currentWorkedHours' => $this->getCurrentWorkedHours(),
            'status' => $this->getStatus(),
        ]);
    }
}
