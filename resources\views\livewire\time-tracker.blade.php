<div class="max-w-4xl mx-auto p-6 space-y-6">
    <!-- Header com horário atual -->
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON> de <PERSON>o</h1>
        <div class="mt-2">
            <span class="text-4xl font-mono text-blue-600 dark:text-blue-400" wire:poll.1s="updateCurrentTime">
                {{ $currentTime }}
            </span>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
            {{ now()->format('l, d \d\e F \d\e Y') }}
        </p>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Card de Status Atual -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status Atual</h3>
            <div class="text-center">
                @if($status === 'not_started')
                    <div class="text-gray-500 dark:text-gray-400">
                        <flux:icon name="clock" class="w-12 h-12 mx-auto mb-2" />
                        <p>Não iniciado</p>
                    </div>
                @elseif($status === 'working')
                    <div class="text-green-600 dark:text-green-400">
                        <flux:icon name="play" class="w-12 h-12 mx-auto mb-2" />
                        <p>Trabalhando</p>
                    </div>
                @elseif($status === 'on_lunch')
                    <div class="text-orange-600 dark:text-orange-400">
                        <flux:icon name="pause" class="w-12 h-12 mx-auto mb-2" />
                        <p>No almoço</p>
                    </div>
                @elseif($status === 'completed')
                    <div class="text-blue-600 dark:text-blue-400">
                        <flux:icon name="check-circle" class="w-12 h-12 mx-auto mb-2" />
                        <p>Concluído</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Card de Horas Trabalhadas -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Horas Trabalhadas</h3>
            <div class="text-center">
                <div class="text-3xl font-mono text-blue-600 dark:text-blue-400" wire:poll.30s>
                    {{ $currentWorkedHours }}
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Meta: {{ $workSchedule->daily_hours }}h
                </p>
            </div>
        </div>

        <!-- Card de Horário Estimado -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Saída Estimada</h3>
            <div class="text-center">
                @if($estimatedClockOut)
                    <div class="text-3xl font-mono text-purple-600 dark:text-purple-400">
                        {{ $estimatedClockOut }}
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Baseado na entrada
                    </p>
                @else
                    <div class="text-gray-500 dark:text-gray-400">
                        <p>--:--</p>
                        <p class="text-sm mt-1">Registre a entrada</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Detalhes do Registro de Hoje -->
    @if($todayEntry)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Registro de Hoje</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
                <span class="text-gray-600 dark:text-gray-400">Entrada:</span>
                <p class="font-mono text-lg">{{ $todayEntry->clock_in ? \Carbon\Carbon::createFromFormat('H:i:s', $todayEntry->clock_in)->format('H:i') : '--:--' }}</p>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">Saída:</span>
                <p class="font-mono text-lg">{{ $todayEntry->clock_out ? \Carbon\Carbon::createFromFormat('H:i:s', $todayEntry->clock_out)->format('H:i') : '--:--' }}</p>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">Almoço Início:</span>
                <p class="font-mono text-lg">{{ $todayEntry->lunch_start ? \Carbon\Carbon::createFromFormat('H:i:s', $todayEntry->lunch_start)->format('H:i') : '--:--' }}</p>
            </div>
            <div>
                <span class="text-gray-600 dark:text-gray-400">Almoço Fim:</span>
                <p class="font-mono text-lg">{{ $todayEntry->lunch_end ? \Carbon\Carbon::createFromFormat('H:i:s', $todayEntry->lunch_end)->format('H:i') : '--:--' }}</p>
            </div>
        </div>

        @if($todayEntry->total_hours)
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <span class="text-gray-600 dark:text-gray-400">Total de Horas:</span>
                <span class="font-semibold text-lg">{{ $todayEntry->total_hours }}h</span>
            </div>
            @if($todayEntry->overtime_hours > 0)
            <div class="flex justify-between items-center mt-2">
                <span class="text-orange-600 dark:text-orange-400">Horas Extras:</span>
                <span class="font-semibold text-lg text-orange-600 dark:text-orange-400">{{ $todayEntry->overtime_hours }}h</span>
            </div>
            @endif
        </div>
        @endif
    </div>
    @endif

    <!-- Botões de Ação -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Ações</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Botão Entrada -->
            <flux:button
                wire:click="clockIn"
                variant="primary"
                class="w-full"
                :disabled="$todayEntry && $todayEntry->clock_in"
            >
                <flux:icon name="play" class="w-5 h-5 mr-2" />
                Registrar Entrada
            </flux:button>

            <!-- Botão Início Almoço -->
            <flux:button
                wire:click="startLunch"
                variant="warning"
                class="w-full"
                :disabled="!$todayEntry || !$todayEntry->clock_in || $todayEntry->lunch_start || $todayEntry->clock_out"
            >
                <flux:icon name="pause" class="w-5 h-5 mr-2" />
                Iniciar Almoço
            </flux:button>

            <!-- Botão Fim Almoço -->
            <flux:button
                wire:click="endLunch"
                variant="warning"
                class="w-full"
                :disabled="!$todayEntry || !$todayEntry->lunch_start || $todayEntry->lunch_end || $todayEntry->clock_out"
            >
                <flux:icon name="play" class="w-5 h-5 mr-2" />
                Finalizar Almoço
            </flux:button>

            <!-- Botão Saída -->
            <flux:button
                wire:click="clockOut"
                variant="danger"
                class="w-full"
                :disabled="!$todayEntry || !$todayEntry->clock_in || $todayEntry->clock_out"
            >
                <flux:icon name="stop" class="w-5 h-5 mr-2" />
                Registrar Saída
            </flux:button>
        </div>

        <!-- Campo de Observações -->
        @if($todayEntry && $todayEntry->clock_in && !$todayEntry->clock_out)
        <div class="mt-6">
            <flux:input
                wire:model="notes"
                label="Observações (opcional)"
                placeholder="Adicione observações sobre o dia de trabalho..."
                type="textarea"
                rows="3"
            />
        </div>
        @endif
    </div>

    <!-- Mensagens de Feedback -->
    @if (session()->has('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif
</div>
