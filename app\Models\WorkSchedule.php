<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkSchedule extends Model
{
    protected $fillable = [
        'user_id',
        'daily_hours',
        'work_days',
        'default_start_time',
        'lunch_break_duration',
        'flexible_schedule',
        'overtime_threshold',
    ];

    protected $casts = [
        'work_days' => 'array',
        'daily_hours' => 'decimal:2',
        'overtime_threshold' => 'decimal:2',
        'flexible_schedule' => 'boolean',
    ];

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Verifica se um dia da semana é dia de trabalho
     */
    public function isWorkDay(string $dayOfWeek): bool
    {
        return in_array(strtolower($dayOfWeek), $this->work_days);
    }

    /**
     * Calcula o horário estimado de saída baseado no horário de entrada
     */
    public function calculateEstimatedClockOut(string $clockInTime): string
    {
        $clockIn = \Carbon\Carbon::createFromFormat('H:i:s', $clockInTime);
        $lunchDuration = \Carbon\Carbon::createFromFormat('H:i:s', $this->lunch_break_duration);

        $estimatedClockOut = $clockIn
            ->addHours($this->daily_hours)
            ->addHours($lunchDuration->hour)
            ->addMinutes($lunchDuration->minute);

        return $estimatedClockOut->format('H:i:s');
    }
}
