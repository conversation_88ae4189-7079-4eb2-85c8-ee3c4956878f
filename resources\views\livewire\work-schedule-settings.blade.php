<div class="max-w-2xl mx-auto p-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Configurações de Horário</h2>

        <form wire:submit.prevent="save" class="space-y-6">
            <!-- <PERSON><PERSON> -->
            <div>
                <flux:input
                    wire:model="dailyHours"
                    label="Horas de Trabalho por Dia"
                    type="number"
                    step="0.25"
                    min="1"
                    max="24"
                    required
                    placeholder="8.00"
                />
                @error('dailyHours')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Dias da Semana -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <PERSON><PERSON> Trabalho
                </label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    @foreach($availableDays as $day => $label)
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input
                                type="checkbox"
                                wire:model="workDays"
                                value="{{ $day }}"
                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            >
                            <span class="text-sm text-gray-700 dark:text-gray-300">{{ $label }}</span>
                        </label>
                    @endforeach
                </div>
                @error('workDays')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Horário Padrão de Entrada -->
            <div>
                <flux:input
                    wire:model="defaultStartTime"
                    label="Horário Padrão de Entrada"
                    type="time"
                    required
                />
                @error('defaultStartTime')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Duração do Almoço -->
            <div>
                <flux:input
                    wire:model="lunchBreakDuration"
                    label="Duração do Almoço"
                    type="time"
                    required
                />
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Tempo que será descontado do total de horas trabalhadas
                </p>
                @error('lunchBreakDuration')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Limite para Hora Extra -->
            <div>
                <flux:input
                    wire:model="overtimeThreshold"
                    label="Limite para Hora Extra (horas)"
                    type="number"
                    step="0.25"
                    min="1"
                    max="24"
                    required
                    placeholder="8.00"
                />
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Horas trabalhadas acima deste limite serão consideradas extras
                </p>
                @error('overtimeThreshold')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Horário Flexível -->
            <div class="flex items-center space-x-3">
                <input
                    type="checkbox"
                    wire:model="flexibleSchedule"
                    id="flexibleSchedule"
                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                >
                <label for="flexibleSchedule" class="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    Horário Flexível
                </label>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Permite variação no horário de entrada e saída, mantendo a carga horária diária
            </p>

            <!-- Botões -->
            <div class="flex flex-col sm:flex-row gap-3 pt-6">
                <flux:button type="submit" variant="primary" class="flex-1">
                    <flux:icon name="check" class="w-5 h-5 mr-2" />
                    Salvar Configurações
                </flux:button>

                <flux:button
                    type="button"
                    wire:click="resetToDefaults"
                    variant="outline"
                    class="flex-1"
                >
                    <flux:icon name="arrow-path" class="w-5 h-5 mr-2" />
                    Restaurar Padrões
                </flux:button>
            </div>
        </form>

        <!-- Mensagens de Feedback -->
        @if (session()->has('success'))
            <div class="mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if (session()->has('error'))
            <div class="mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif
    </div>
</div>
