<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('date'); // Data do registro
            $table->time('clock_in')->nullable(); // Horário de entrada
            $table->time('clock_out')->nullable(); // Horário de saída
            $table->time('lunch_start')->nullable(); // Início do almoço
            $table->time('lunch_end')->nullable(); // Fim do almoço
            $table->decimal('total_hours', 4, 2)->nullable(); // Total de horas trabalhadas
            $table->decimal('overtime_hours', 4, 2)->default(0); // Horas extras
            $table->text('notes')->nullable(); // Observações
            $table->enum('status', ['in_progress', 'completed'])->default('in_progress');
            $table->timestamps();

            // Índices para melhor performance
            $table->index(['user_id', 'date']);
            $table->unique(['user_id', 'date']); // Um registro por usuário por dia
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_entries');
    }
};
