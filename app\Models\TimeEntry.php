<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TimeEntry extends Model
{
    protected $fillable = [
        'user_id',
        'date',
        'clock_in',
        'clock_out',
        'lunch_start',
        'lunch_end',
        'total_hours',
        'overtime_hours',
        'notes',
        'status',
    ];

    protected $casts = [
        'date' => 'date',
        'total_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
    ];

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calcula o total de horas trabalhadas
     */
    public function calculateTotalHours(): void
    {
        if (!$this->clock_in || !$this->clock_out) {
            return;
        }

        $clockIn = Carbon::createFromFormat('H:i:s', $this->clock_in);
        $clockOut = Carbon::createFromFormat('H:i:s', $this->clock_out);

        $totalMinutes = $clockOut->diffInMinutes($clockIn);

        // Subtrai o tempo de almoço se definido
        if ($this->lunch_start && $this->lunch_end) {
            $lunchStart = Carbon::createFromFormat('H:i:s', $this->lunch_start);
            $lunchEnd = Carbon::createFromFormat('H:i:s', $this->lunch_end);
            $lunchMinutes = $lunchEnd->diffInMinutes($lunchStart);
            $totalMinutes -= $lunchMinutes;
        }

        $this->total_hours = round($totalMinutes / 60, 2);

        // Calcula horas extras baseado na configuração do usuário
        $workSchedule = $this->user->workSchedule;
        if ($workSchedule && $this->total_hours > $workSchedule->overtime_threshold) {
            $this->overtime_hours = $this->total_hours - $workSchedule->overtime_threshold;
        } else {
            $this->overtime_hours = 0;
        }
    }

    /**
     * Verifica se o registro está em andamento
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Marca o registro como completo
     */
    public function markAsCompleted(): void
    {
        $this->status = 'completed';
        $this->calculateTotalHours();
        $this->save();
    }
}
