<x-layouts.app :title="__('Controle de Horas')">
    <div class="space-y-8">
        <!-- Navegação por Tabs -->
        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    x-data="{ active: true }"
                    @click="
                        $wire.set('activeTab', 'tracker');
                        document.querySelectorAll('[data-tab]').forEach(el => el.classList.add('hidden'));
                        document.querySelector('[data-tab=tracker]').classList.remove('hidden');
                        document.querySelectorAll('[data-tab-btn]').forEach(el => el.classList.remove('border-blue-500', 'text-blue-600'));
                        $el.classList.add('border-blue-500', 'text-blue-600');
                    "
                    data-tab-btn="tracker"
                    class="border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                >
                    <flux:icon name="clock" class="w-5 h-5 mr-2 inline" />
                    Controle de Ponto
                </button>
                
                <button 
                    @click="
                        $wire.set('activeTab', 'history');
                        document.querySelectorAll('[data-tab]').forEach(el => el.classList.add('hidden'));
                        document.querySelector('[data-tab=history]').classList.remove('hidden');
                        document.querySelectorAll('[data-tab-btn]').forEach(el => el.classList.remove('border-blue-500', 'text-blue-600'));
                        $el.classList.add('border-blue-500', 'text-blue-600');
                    "
                    data-tab-btn="history"
                    class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                >
                    <flux:icon name="calendar-days" class="w-5 h-5 mr-2 inline" />
                    Histórico
                </button>
                
                <button 
                    @click="
                        $wire.set('activeTab', 'settings');
                        document.querySelectorAll('[data-tab]').forEach(el => el.classList.add('hidden'));
                        document.querySelector('[data-tab=settings]').classList.remove('hidden');
                        document.querySelectorAll('[data-tab-btn]').forEach(el => el.classList.remove('border-blue-500', 'text-blue-600'));
                        $el.classList.add('border-blue-500', 'text-blue-600');
                    "
                    data-tab-btn="settings"
                    class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                >
                    <flux:icon name="cog-6-tooth" class="w-5 h-5 mr-2 inline" />
                    Configurações
                </button>
            </nav>
        </div>

        <!-- Conteúdo das Tabs -->
        <div>
            <!-- Tab Controle de Ponto -->
            <div data-tab="tracker">
                @livewire('time-tracker')
            </div>

            <!-- Tab Histórico -->
            <div data-tab="history" class="hidden">
                @livewire('time-history')
            </div>

            <!-- Tab Configurações -->
            <div data-tab="settings" class="hidden">
                @livewire('work-schedule-settings')
            </div>
        </div>
    </div>
</x-layouts.app>
